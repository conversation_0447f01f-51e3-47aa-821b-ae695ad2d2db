#ifndef _LIST_H_
#define _LIST_H_

// includes
#include "record.h"

// data definitions
typedef record_t* data_t;
typedef struct node node_t;

struct node {
    data_t data;
    node_t *next;
};

typedef struct {
    node_t *head;
    node_t *foot;
} list_t;

// function definitions
list_t *make_empty_list(void);
int is_empty_list(list_t *list);
list_t *insert_at_foot(list_t *list, data_t value);

#endif
