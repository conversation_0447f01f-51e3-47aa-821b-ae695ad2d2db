**Your Task**

**Background**

A dictionary is an abstract data type that stores a collection of data records and supports **lookup**

of a key, **deletion** of a key and **insertion** of a data record. For example, in a telephone directory,

the (string) key is a person or company name, and the data record is the tuple (name, phone

number). In a student record lookup, the key would be a student ID number and the data record

would be a complex structure containing the student ID and all the other information about the

student. In this assignment, the key will be an address string and the data record will be the

information that corresponds to that address.

**Assignment**

In this assignment, you will create a simple **dictionary** based on a **linked list** to store information

about Victorian addresses from a dataset. Users will be able to perform **search** queries on this

dictionary, i.e., searching for address records using attributes in the dataset (keys).

**C Implementation**

The provided dataset has the following 35 fields:

```
PFI - Persistent Feature Identifier - an identifier that refers to the

same address throughout its life (string)

EZI_ADD - Easy Address - combines information from other fields into an

easy-to-read string, can be made from other fields (string)

SRC_VERIF - Source Verified Date - the date the address was verified to have

all its attributes (if it has been) (string)

FLOOR_NO_2 - Floor Number 2 - the second floor number which an address refers

to, used where an address spans multiple floors. (integer)

BUILDING - Building Name - Common usage name for address site (string)

COMPLEX - Complex Name - Common usage name for a complex or group of

addressed properties (e.g. MELBOURNE AIRPORT) (string)

HSE_PREF1 - House Prefix 1 - The letters preceding the first house number

(e.g. K in K189E SOUTH CENTRE ROAD TULLAMARINE 3043) (string)

HSE_NUM1 - House Number 1 - the first house number in an address (integer)

HSE_SUF1 - House Suffix 1 - The letters following the first house number

(e.g. E in K189E SOUTH CENTRE ROAD TULLAMARINE 3043) (string)

HSE_PREF2 - House Prefix 2 - The letters preceding the second house number

in a range (e.g. the second A in A1-A8 LAKESIDE VILLAGE DRIVE LILYDALE 3140)

(string)

HSE_NUM2 - House Number 2 - The second house number in the range of an

address (e.g. 8 in A1-A8 LAKESIDE VILLAGE DRIVE LILYDALE 3140) (integer)

HSE_SUF2 - House Suffix 2 - The letters following the first house number

(e.g. GR in 34-36GR HOWE CRESCENT SOUTH MELBOURNE 3205) (string)

DISP_NUM1 - Display Number 1 - The number displayed on the property, where

different to the officially assigned numbers (mainly in the CBD) (integer)

ROAD_NAME - Road Name - The name of the road component of the address (e.g.

DEAKIN in 12 DEAKIN AVENUE EX KOORLONG 3501) (string)

ROAD_TYPE - Road Type - The type of the road (e.g. AVENUE in 12 DEAKIN

AVENUE EX KOORLONG 3501) (string)

RD_SUF - Road Suffix - The abbreviation of the road type classification

(e.g. EX in 12 DEAKIN AVENUE EX KOORLONG 3501) (string)

LOCALITY - Locality Name - The name of the locality (e.g. KOORLONG in 12

DEAKIN AVENUE EX KOORLONG 3501) (string)
```

This data is in CSV format, with each field separated by a **comma**. For the purposes of the

assignment, you may assume that:

the input data is well-formatted,

input files are not empty,

input files **include a header line** that contains the filled headers in the CSV format,

each field contains **at most 127** characters,

each field with the "integer" type in the list above **may be empty**,

fields always occur in the order given above and,

the **maximum length** of an input record (a single full **line** of the CSV) is **511 characters**.

Optionally, if you would like to **extend** your program to handle arbitrary CSV field contents, you

may extend the functionality of your program with the following assumptions, these extensions

are **not required** to handle the datasets used during testing:

any **double quotation mark** to be interpreted as a double quotation mark ( " ) in the field

appears **twice** (e.g. "" after the initial quote corresponds to a " in the actual string shown in

the CSV),

any field containing quotation marks will begin with a double quotation mark ( " ) and end

with a quotation mark ( " ),

any field which does not close an opened quote continues on the following line and,

any field containing a comma will begin with a double quotation mark ( " ) and end with a

The number of columns (**35** in this assignment), as well as the **order**, **text** and **data type** of

each column are assumed to be **known**. While it's possible to **automatically determine** this

information, it's **beyond the scope** of this assignment

**Implementation Details**

Assignment 1 will involve producing one dictionary program (with later programs and stages

produced in Assignment 2).

Stage 1 will implement the **lookup** of data by a given **key** ( EZI_ADD )

**Stage 1 - Data Retrieval**

In Stage 1, you will implement the basic functionality for a dictionary allowing the lookup of data

by key ( EZI_ADD）

Your Makefile should produce an executable program called dict1 . This program should take



Bit accesses can be counted until the first mismatch from the left, with bits

compared only counted until the first mismatch. You may assume, even if your

function needs to read a whole byte to extract a bit, that it only counts as a single

bit access.

The node access is incremented once per accessed linked list node. 

Each string comparison, even if a mismatch occurs on the first bit, is 1 string

comparison

You can assume all queries will be terminated by a newline character, allowing queries for

entries with empty address names. While you will have to retrieve the field headers for each

column, you can assume you know the field referred to as EZI_ADD , is the second (0-based

index) column in the dataset.

For testing, it may be convenient to create a file of keys to be searched, one per line, similar to 

tests/test2.in and redirect the input from this file. Use the UNIX operator < to redirect input

from a file

where line 1 is for compiling, line 2 is for running the program. Line 2 specifies that the task is a

search (indicated by the value 1). The program will search the dataset tests/dataset_22.csv and

write the results to output.txt . Lines 3 to 5 provide the search queries: " 18 PROFESSORS WALK

PARKVILLE 3052 ", " 783 SWANSTON STREET PARKVILLE 3052 ", and " 230 GRATTAN STREET

PARKVILLE 3052 "

**Example Output** 

This is an example of what might be output to the *output file* after three queries

```
18 PROFESSORS WALK PARKVILLE 3052

--> PFI: 422335994 || EZI_ADD: 18 PROFESSORS WALK PARKVILLE 3052 ||

SRC_VERIF: 2024-12-16 || PROPSTATUS: A || GCODEFEAT: V || LOC_DESC: ||

BLGUNTTYP: || HSAUNITID: || BUNIT_PRE1: || BUNIT_ID1: || BUNIT_SUF1: ||

BUNIT_PRE2: || BUNIT_ID2: || BUNIT_SUF2: || FLOOR_TYPE: || FLOOR_NO_1:

|| FLOOR_NO_2: || BUILDING: OLD ARTS BUILDING 149 || COMPLEX: || HSE_PREF1:

|| HSE_NUM1: 18.0 || HSE_SUF1: || HSE_PREF2: || HSE_NUM2: || HSE_SUF2: ||

DISP_NUM1: || ROAD_NAME: PROFESSORS || ROAD_TYPE: WALK || RD_SUF: ||

LOCALITY: PARKVILLE || STATE: VIC || POSTCODE: 3052 || ACCESSTYPE: L || x:

144.96013 || y: -37.79772 ||

783 SWANSTON STREET PARKVILLE 3052

--> PFI: 422335968 || EZI_ADD: 783 SWANSTON STREET PARKVILLE 3052 ||

SRC_VERIF: 2024-12-16 || PROPSTATUS: A || GCODEFEAT: V || LOC_DESC: ||

BLGUNTTYP: || HSAUNITID: || BUNIT_PRE1: || BUNIT_ID1: || BUNIT_SUF1: ||

BUNIT_PRE2: || BUNIT_ID2: || BUNIT_SUF2: || FLOOR_TYPE: || FLOOR_NO_1:

|| FLOOR_NO_2: || BUILDING: SIDNEY MYER ASIA CENTRE BUILDING 158 || COMPLEX:
...
```

With the following output to stdout:

```
18 PROFESSORS WALK PARKVILLE 3052 --> 1 records found - comparisons: b418 n22

s22

783 SWANSTON STREET PARKVILLE 3052 --> 1 records found - comparisons: b406 n22

s22

230 GRATTAN STREET PARKVILLE 3052 --> 20 records found - comparisons: b5453

n22 s22
```

**Hints**

To process a csv file, it is usually easiest to read the entire line and then read through the

characters. To help with this, a simple *state machine* has been provided - arrows show state

changes and the conditions on those changes, diamonds show actions and squares show where

the process starts. You might find it useful to have your program handle whether it is in the pink

state or the blue state. Your program isn't required to follow this pattern, but it may allow your code to be simpler