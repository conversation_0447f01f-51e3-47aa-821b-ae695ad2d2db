# 字典数据结构实现项目需求文档

## 项目背景

字典是一种抽象数据类型，用于存储数据记录集合，支持以下三种基本操作：
- **查找**：根据键查找对应的数据记录
- **删除**：删除指定键的数据记录  
- **插入**：插入新的数据记录

例如：
- 电话簿中，键是人名或公司名（字符串），数据记录是（姓名，电话号码）元组
- 学生记录查找中，键是学生ID号，数据记录是包含学生ID和其他学生信息的复杂结构

在本项目中，键将是地址字符串，数据记录将是与该地址对应的相关信息。

## 项目任务

本项目要求创建一个基于**链表**的简单**字典**，用于存储维多利亚州地址数据集的信息。用户可以对该字典执行**搜索**查询，即使用数据集中的属性（键）搜索地址记录。

## C语言实现要求

### 数据集字段说明

提供的数据集包含以下35个字段(列表中不全）：

| 字段名 | 中文描述 | 数据类型 | 说明 |
|--------|----------|----------|------|
| PFI | 持久特征标识符 | 字符串 | 在地址整个生命周期中引用同一地址的标识符 |
| EZI_ADD | 易读地址 | 字符串 | 将其他字段信息组合成易读字符串，可由其他字段生成 |
| SRC_VERIF | 源验证日期 | 字符串 | 地址被验证具有所有属性的日期（如果已验证） |
| FLOOR_NO_2 | 楼层号2 | 整数 | 地址引用的第二个楼层号，用于地址跨多个楼层的情况 |
| BUILDING | 建筑名称 | 字符串 | 地址站点的常用名称 |
| COMPLEX | 综合体名称 | 字符串 | 综合体或地址属性组的常用名称（如墨尔本机场） |
| HSE_PREF1 | 房屋前缀1 | 字符串 | 第一个房屋号码前的字母 |
| HSE_NUM1 | 房屋号码1 | 整数 | 地址中的第一个房屋号码 |
| HSE_SUF1 | 房屋后缀1 | 字符串 | 第一个房屋号码后的字母 |
| HSE_PREF2 | 房屋前缀2 | 字符串 | 范围中第二个房屋号码前的字母 |
| HSE_NUM2 | 房屋号码2 | 整数 | 地址范围中的第二个房屋号码 |
| HSE_SUF2 | 房屋后缀2 | 字符串 | 第一个房屋号码后的字母 |
| DISP_NUM1 | 显示号码1 | 整数 | 属性上显示的号码，与官方分配号码不同时使用 |
| ROAD_NAME | 道路名称 | 字符串 | 地址的道路组件名称 |
| ROAD_TYPE | 道路类型 | 字符串 | 道路类型 |
| RD_SUF | 道路后缀 | 字符串 | 道路类型分类的缩写 |
| LOCALITY | 地区名称 | 字符串 | 地区名称 |

### 数据格式假设

数据为CSV格式，每个字段用**逗号**分隔。项目可以假设：

1. 输入数据格式良好
2. 输入文件非空
3. 输入文件**包含标题行**，包含CSV格式的填充标题
4. 每个字段最多包含**127个字符**
5. 上述列表中"整数"类型的字段**可能为空**
6. 字段始终按上述给定顺序出现
7. 输入记录（CSV的单个完整**行**）的**最大长度**为**511个字符**

### 可选扩展功能

如果希望**扩展**程序以处理任意CSV字段内容，可以使用以下假设扩展程序功能（这些扩展在测试期间使用的数据集中**不是必需的**）：

1. 要解释为双引号(")的任何**双引号**在初始引号后出现**两次**（例如""对应实际字符串中的"）
2. 包含引号的任何字段将以双引号(")开始并以引号(")结束
3. 任何未关闭已打开引号的字段在下一行继续
4. 包含逗号的任何字段将以双引号(")开始并以双引号(")结束

列数（本项目中为**35**）以及每列的**顺序**、**文本**和**数据类型**假设为**已知**。虽然可以**自动确定**这些信息，但这**超出了本项目的范围**。

## 实现细节

### 第一阶段 - 数据检索

第一阶段将实现字典的基本功能，允许通过键（EZI_ADD）查找数据。

#### 程序要求

1. Makefile应生成名为`dict1`的可执行程序
2. 程序应接受命令行参数
3. 实现通过给定**键**（EZI_ADD）**查找**数据的功能

#### 性能统计

程序需要统计以下性能指标：

- **位访问**：可以计算到从左侧第一次不匹配为止的位访问，只计算到第一次不匹配的比较位
- **节点访问**：每访问一个链表节点增加一次
- **字符串比较**：每次字符串比较计为1次，即使在第一位就发生不匹配

#### 输入输出格式

- 所有查询将以换行符终止，允许查询空地址名称的条目
- 需要检索每列的字段标题
- 可以假设知道EZI_ADD字段是数据集中的第二列（基于0的索引）

#### 测试建议

为便于测试，可以创建一个包含要搜索的键的文件，每行一个键，类似于`tests/test2.in`，并使用UNIX操作符`<`从文件重定向输入。

## 示例输出

### 输出文件示例
```
18 PROFESSORS WALK PARKVILLE 3052
--> PFI: 422335994 || EZI_ADD: 18 PROFESSORS WALK PARKVILLE 3052 ||
SRC_VERIF: 2024-12-16 || PROPSTATUS: A || GCODEFEAT: V || LOC_DESC: ||
BLGUNTTYP: || HSAUNITID: || BUNIT_PRE1: || BUNIT_ID1: || BUNIT_SUF1: ||
BUNIT_PRE2: || BUNIT_ID2: || BUNIT_SUF2: || FLOOR_TYPE: || FLOOR_NO_1:
|| FLOOR_NO_2: || BUILDING: OLD ARTS BUILDING 149 || COMPLEX: || HSE_PREF1:
|| HSE_NUM1: 18.0 || HSE_SUF1: || HSE_PREF2: || HSE_NUM2: || HSE_SUF2: ||
DISP_NUM1: || ROAD_NAME: PROFESSORS || ROAD_TYPE: WALK || RD_SUF: ||
LOCALITY: PARKVILLE || STATE: VIC || POSTCODE: 3052 || ACCESSTYPE: L || x:
144.96013 || y: -37.79772 ||
```

### 标准输出示例
```
18 PROFESSORS WALK PARKVILLE 3052 --> 1 records found - comparisons: b418 n22 s22
783 SWANSTON STREET PARKVILLE 3052 --> 1 records found - comparisons: b406 n22 s22
230 GRATTAN STREET PARKVILLE 3052 --> 20 records found - comparisons: b5453 n22 s22
```

## 实现提示

处理CSV文件时，通常最简单的方法是读取整行，然后逐字符读取。为了帮助实现，可以使用简单的**状态机**模式：
- 箭头显示状态变化和变化条件
- 菱形显示操作
- 方形显示过程开始位置

程序可以处理是否处于不同状态。虽然程序不需要遵循此模式，但它可能使代码更简单。
