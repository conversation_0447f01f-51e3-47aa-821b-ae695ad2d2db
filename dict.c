#include "dict.h"
#include "bit.h"

dict_t* make_dictionary(FILE *file) {
    
    // 创造一个空的dict
    dict_t* dictionary = make_empty_list();
    char line_buffer[MAX_LINE_LEN + 1];
    
    // 跳过header
    fgets(line_buffer, MAX_LINE_LEN + 1, file);
    
    // 循环读文件的每一行内容
    while (fgets(line_buffer, MAX_LINE_LEN + 1, file) != NULL) {
        
        // 将这一行存为一个record_t struct
        record_t *record = make_record(line_buffer);
        
        // 加入到dictionary里面
        dictionary = insert_at_foot(dictionary, record);
    }
    
    return dictionary;
}

void print_dictionary(dict_t *dictionary) {
    node_t *curr = dictionary->head;
    
    while (curr) {
        print_record(stdout, curr->data);
        curr = curr->next;
    }
}

result_t *search_dictionary(FILE *file, dict_t *dictionary, char *key) {
    
    result_t *result = make_result(key);
    node_t *curr = dictionary->head;
    
    fprintf(file, "%s\n", key);
    while (curr) {
        
        // 检查节点里面储存的返回的EZI_ADD是不是search_key一样
        if (strcmp_by_bit(curr->data->EZI_ADD, key, &(result->bit_cmp), &(result->str_cmp)) == 0) {
            print_record(file, curr->data);
            result->num_match++;
        }
        
        result->node_access++;
        curr = curr->next;
    }
    
    return result;
}

void free_dictionary(dict_t *dictionary) {

    node_t *curr = dictionary->head;
    node_t *prev;

    while (curr) {
        prev = curr;
        curr = curr->next;

        free_record(prev->data);
        free(prev);
    }

    free(dictionary);
}
