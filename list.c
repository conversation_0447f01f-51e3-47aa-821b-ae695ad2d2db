#include <stdio.h>
#include <stdlib.h>
#include <assert.h>
#include "list.h"

// function implementation
list_t
*make_empty_list(void) {
    
    // 我们想要一个list_t pointer, 指向一个list_t struct
    list_t* list;
    
    // malloc了一个list_t struct的空间, 然后把pointer赋值给list这个变量
    list = (list_t*)malloc(sizeof(list_t));
    assert(list);
    
    // 让head和foot都指向NULL
    list->head = NULL;
    list->foot = NULL;
    
    return list;
}

int
is_empty_list(list_t *list) {
    assert(list);

    if (list->head == NULL) {
        return 1;
    } else {
        return 0;
    }
}

list_t
*insert_at_foot(list_t *list, data_t value) {
    node_t* new_node = (node_t*)malloc(sizeof(node_t));
    assert(new_node && list);

    new_node->data = value;
    new_node->next = NULL;

    if (list->head == NULL) {
        list->head = new_node;
    } else {
        list->foot->next = new_node;
    }

    list->foot = new_node;
    return list;
}
