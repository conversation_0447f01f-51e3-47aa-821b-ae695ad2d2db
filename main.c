// include
#include <stdlib.h>
#include <stdio.h>
#include "dict.h"

// constant, structure, typedef

#define CMD_LN_ARGC 4
#define STAGE_ONE 1

// function prototypes


// function implementations
int main(int argc, char **argv) {
    
    // 检查command line argument
    if (argc != CMD_LN_ARGC) {
        fprintf(stderr, "Usage: dict stage datafile outputfile");
        exit(EXIT_FAILURE);
    }
    
    if (atoi(argv[1]) != STAGE_ONE) {
        fprintf(stderr, "Usage: dict stage datafile outputfile");
        exit(EXIT_FAILURE);
    }
    
    // 开始程序流程
    
    // 1. 打开文件
    FILE *in_file = fopen(argv[2], "r");
    assert(in_file != NULL);
    
    FILE *out_file = fopen(argv[3], "w");
    assert(out_file != NULL);
    
    // 2. 读取文件，并且存为字典
    dict_t *dictionary = make_dictionary(in_file);
    // CHECK POINT: 此时dictionary里面的records应该是正确的，所有field都被读取并存储，顺序对不对
    // print_dictionary(dictionary);
    
    // 3. 搜索 和 打印结果
    
    // 4. 善后工作
    
    return 0;
}
